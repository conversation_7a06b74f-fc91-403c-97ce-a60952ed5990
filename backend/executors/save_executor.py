import time
from datetime import datetime
from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from elasticsearch import Elasticsearch, helpers
from yfflow import YfExecutor, requests
from yunfu.common import ConfigUtils, yfid

from backend.graph import get_graph_mapper
from backend.models.kg import Kg
from backend.utils import GraphUtils


class SaveExecutor(YfExecutor):

    config = ConfigUtils.load("conf/config.yaml")
    es = Elasticsearch(config["ES_CONFIGS"]["host"], timeout=180)
    index = config["ES_CONFIGS"]["save_index"]

    @requests(on="/create")
    async def create(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        for doc in docs:
            documents = doc.tags.get("documents")
            # documents: {eid: data}
            self.logger.info(f"receive documents: {documents}")
            if documents:
                await sync_to_async(self._create)(documents)

    def _create(self, documents: list) -> None:
        # 1、声明空列表actions
        actions = []
        # 2、遍历documents，获取data和eid，生成action,并添加到actions中
        for document in documents:
            eid = list(document.keys())[0]
            data = document.get(eid)
            es_id = data.get("es_id")
            if not es_id:
                es_id = yfid(f"{eid}_{time.time()}")
            action = {
                "_index": self.index,
                "_id": es_id,
                "_type": "_doc",
                "_source": data,
            }
            actions.append(action)
        # 3、批量插入
        helpers.bulk(self.es, actions, refresh=True)
        self.logger.info("success insert documents!")

    @requests(on="/reindex")
    async def reindex(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        for doc in docs:
            kg_id = doc.tags.get("kg_id")
            version = doc.tags.get("version", "c")
            self.logger.info(f"receive kg_id: {kg_id}")
            if kg_id:
                await sync_to_async(self._reindex)(int(kg_id), version)

    def _reindex(self, kg_id: int, version: str) -> None:
        try:
            # 1、根据kg_id去es中批量删除kg_id对应的数据
            self.logger.info(f"delete kg_id data: {kg_id}")
            # TODO es数据删除完成判断
            self.es.delete_by_query(
                index=self.index,
                body={"query": {"bool": {"must": [{"match": {"kg_id": kg_id}}]}}},
                conflicts="proceed",
            )
        except Exception as e:
            self.logger.error(e)
            return
        # 2、根据kg_id去neo4j中查询数据,并批量插入到es中
        self._search_and_save(kg_id, version)

    def _search_and_save(self, kg_id: int, version: str) -> None:
        space = GraphUtils.get_label(kg_id)
        kg = Kg.objects.get(id=abs(kg_id))
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        if kg_id < 0:
            space = f"KG{kg_id * -1}_ontology"
        ontology_label = GraphUtils.get_ontology_label(kg_id)
        count = graph_mapper.get_nodes_count(space)
        self.logger.info(f"count: {count}")
        limit = self.config["NODES_LIMIT"]
        for i in range(0, int(count / limit) + 1):
            self.logger.info(f"space: {space}, page: {i}")
            # 1、根据labels去neo4j中查询指定数量的结点
            nodes = graph_mapper.get_nodes(space, i * limit, limit)
            self.logger.info(f"nodes is : {nodes}")
            # 2、遍历结点，获取eid和data，生成action,并添加到actions中
            documents = []
            for node in nodes:
                data = {}
                eid = node.props.get("_eid")
                location = None
                relations = graph_mapper.get_all_relations_by_eid(space, eid)
                try:
                    location = {
                        "type": "point",
                        "coordinates": [
                            float(node.props.get("lng") or node.props.get("经度")),
                            float(node.props.get("lat") or node.props.get("纬度")),
                        ],
                    }
                except (ValueError, TypeError):
                    pass
                for key, value in node.props.items():
                    if key.startswith("name@"):
                        data["_".join(key.split("@"))] = value
                    if key.startswith("_type@"):
                        data["_".join(key.split("@"))[1:]] = value
                data["name"] = node.props.get("name")
                data["show_name"] = node.props.get("_show_name")
                data["alias"] = node.props.get("alias")
                data["avatar"] = node.props.get("_avatar")
                data["source_type"] = 3
                data["type"] = node.props.get("_type")
                data["status"] = 1
                data["created_at"] = datetime.now()
                data["updated_at"] = datetime.now()
                data["version"] = [
                    label
                    for label in node.types
                    if label.startswith("v") or label == "c"
                ]
                data["kg_id"] = kg_id
                data["node_type"] = "ont" if ontology_label in node.types else "entity"
                data["eid"] = eid
                data["es_id"] = node.props.get("es_id")
                data["location"] = location
                data["props"] = list(node.props.keys())
                data["relations"] = [relation.props["name"] for relation in relations]

                documents.append({eid: data})
            self.logger.info(f"insert documents: {documents}")
            # 3、批量插入
            self._create(documents)
